# add_seren_to_frappe_min.py
import os, sys
import frappe

SITE_NAME  = "32016-51127.bacloud.info"
BENCH_PATH = "/home/<USER>/frappe-bench"
SITES_PATH = os.path.join(BENCH_PATH, "sites")
APPS_PATH  = os.path.join(BENCH_PATH, "apps")
sys.path.extend([BENCH_PATH, APPS_PATH, os.path.join(APPS_PATH, "frappe")])

def _discover_bench_and_sites():
    """Discover bench and sites paths relative to current environment."""
    return BENCH_PATH, SITES_PATH

# Global flag to track if Frappe has been initialized
_frappe_initialized = False

def _ensure_frappe_ready():
    """Initialize Frappe deterministically regardless of CWD.
    - Discover bench/sites relative to this file.
    - Export FRAPPE_SITE and FRAPPE_SITES_PATH for Frappe internals.
    - Ensure app and site log dirs exist *before* frappe.connect() to prevent logger errors.
    """
    global _frappe_initialized

    # Skip if already initialized
    if _frappe_initialized:
        return

    bench_path, sites_path = _discover_bench_and_sites()

    # Export canonical env vars so Frappe resolves paths correctly
    os.environ.setdefault("FRAPPE_SITE", SITE_NAME)
    os.environ.setdefault("FRAPPE_SITES_PATH", sites_path)
    os.environ.setdefault("SITES_HOME", sites_path)  # legacy/compat

    # CRITICAL: Change to bench directory BEFORE creating log directories
    # This ensures Frappe's relative path resolution works correctly
    original_cwd = os.getcwd()
    try:
        os.chdir(bench_path)
    except Exception as e:
        print(f"Warning: Could not change to bench directory {bench_path}: {e}")

    # Ensure sys.path contains discovered bench locations
    if bench_path not in sys.path:
        sys.path.append(bench_path)
    apps_path = os.path.join(bench_path, "apps")
    if apps_path not in sys.path:
        sys.path.append(apps_path)

    # --- Ensure ALL possible log dirs exist BEFORE init/connect ---
    # Bench-level logs directory (for general frappe logs)
    bench_logs = os.path.join(bench_path, "logs")
    os.makedirs(bench_logs, exist_ok=True)

    # App-level logs directory (this was causing the first error)
    app_logs = os.path.join(bench_path, "apps", "logs")
    os.makedirs(app_logs, exist_ok=True)

    # Site logs path computed from discovered sites path and SITE_NAME
    site_logs = os.path.join(sites_path, SITE_NAME, "logs")
    os.makedirs(site_logs, exist_ok=True)

    # Additional log directories that Frappe might try to create based on CWD
    # These handle the case where Frappe constructs paths relative to current directory
    current_dir_logs = os.path.join(os.getcwd(), "logs")
    os.makedirs(current_dir_logs, exist_ok=True)

    # Site-specific logs in current directory (handles the error pattern we saw)
    current_site_logs = os.path.join(os.getcwd(), SITE_NAME, "logs")
    os.makedirs(current_site_logs, exist_ok=True)

    # Initialize/connect once. Use discovered sites_path.
    if not getattr(frappe.local, "site", None):
        frappe.init(site=SITE_NAME, sites_path=sites_path)
    if not getattr(frappe.local, "db", None):
        frappe.connect()

    # Mark as initialized
    _frappe_initialized = True

# Don't call initialization at import time - let functions call it when needed
# _ensure_frappe_ready()

def add_seren_to_frappe(agent: dict) -> dict:
	# Ensure Frappe is properly initialized before using it
	_ensure_frappe_ready()

	docname = agent.get("name") or agent.get("agent_name")
	if not docname:
		raise ValueError("agent must include 'agent_name' or 'name'")

	if frappe.db.exists("Agents", docname):
		doc = frappe.get_doc("Agents", docname)
		doc.agent_name = agent.get("agent_name") or docname
		doc.title = agent.get("title")
		doc.telegram_api_hash = agent.get("telegram_api_hash")
		doc.telegram_session_name = agent.get("telegram_session_name")
		doc.telegram_api_id = agent.get("telegram_api_id")
		doc.telegram_phone = agent.get("telegram_phone")
		doc.disabled = 1
		doc.save(ignore_permissions=True)
		frappe.db.commit()
	else:
		doc = frappe.new_doc("Agents")
		doc.name = docname
		doc.flags.name_set_by_user = True
		doc.agent_name = agent.get("agent_name") or docname
		doc.title = agent.get("title")
		doc.telegram_api_hash = agent.get("telegram_api_hash")
		doc.telegram_session_name = agent.get("telegram_session_name")
		doc.telegram_api_id = agent.get("telegram_api_id")
		doc.telegram_phone = agent.get("telegram_phone")
		doc.disabled = 1
		doc.insert(ignore_permissions=True, ignore_if_duplicate=True)
		frappe.db.commit()

	return {"name": doc.name}
